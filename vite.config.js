import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  // ==================== 共享选项 ====================
  
  // 项目根目录（index.html 文件所在的位置）
  root: process.cwd(),
  
  // 开发或生产环境服务的公共基础路径
  // 可以是绝对路径如 '/foo/'，完整URL如 'https://bar.com/foo/'，或空字符串 './'
  base: '/',
  
  // 指定模式，会覆盖 serve 和 build 时的模式
  // 可选值：'development' | 'production' | 自定义模式
  mode: 'development',
  
  // 定义全局常量替换方式，在开发环境下定义在全局，构建时被静态替换
  define: {
    __APP_VERSION__: JSON.stringify('v1.0.0'),
    __API_URL__: 'window.__backend_api_url',
  },
  
  // 需要用到的插件数组
  plugins: [
    vue(), // Vue 插件
    // 其他插件...
  ],
  
  // 作为静态资源服务的文件夹，设置为 false 可以关闭此功能
  publicDir: 'public',
  
  // 存储缓存文件的目录
  cacheDir: 'node_modules/.vite',
  
  // ==================== 解析选项 ====================
  resolve: {
    // 路径别名配置
    alias: {
      '@': '/src',
      '@components': '/src/components',
      '@utils': '/src/utils',
    },
    
    // 强制 Vite 始终将列出的依赖项解析为同一副本（用于 monorepos）
    dedupe: [],
    
    // 解决程序包中情景导出时的其他允许条件
    conditions: ['module', 'browser', 'development'],
    
    // package.json 中，在解析包的入口点时尝试的字段列表
    mainFields: ['browser', 'module', 'jsnext:main', 'jsnext'],
    
    // 导入时想要省略的扩展名列表
    extensions: ['.mjs', '.js', '.mts', '.ts', '.jsx', '.tsx', '.json'],
    
    // 启用此选项会使 Vite 通过原始文件路径确定文件身份
    preserveSymlinks: false,
  },
  
  // ==================== HTML 相关 ====================
  html: {
    // 内容安全策略（CSP）的 nonce 值占位符
    cspNonce: '',
  },
  
  // ==================== CSS 相关 ====================
  css: {
    // CSS modules 配置
    modules: {
      scopeBehaviour: 'local',
      generateScopedName: '[name]__[local]___[hash:base64:5]',
      hashPrefix: '',
      localsConvention: 'camelCase',
    },
    
    // PostCSS 配置
    postcss: {
      // 可以是字符串路径或内联配置对象
    },
    
    // CSS 预处理器选项
    preprocessorOptions: {
      scss: {
        additionalData: `$injectedColor: orange;`,
      },
      less: {
        math: 'parens-division',
      },
    },
    
    // CSS 预处理器最大线程数
    preprocessorMaxWorkers: true,
    
    // 开发过程中是否启用 sourcemap
    devSourcemap: false,
    
    // CSS 处理引擎选择：'postcss' | 'lightningcss'
    transformer: 'postcss',
    
    // Lightning CSS 配置（当 transformer 为 'lightningcss' 时使用）
    lightningcss: {
      // targets: {},
      // include: {},
      // exclude: {},
    },
  },
  
  // ==================== JSON 相关 ====================
  json: {
    // 是否支持从 .json 文件中进行按名导入
    namedExports: true,
    
    // 是否将导入的 JSON 转换为 export default JSON.parse("...")
    stringify: 'auto', // true | false | 'auto'
  },
  
  // ==================== esbuild 配置 ====================
  esbuild: {
    // JSX 工厂函数
    jsxFactory: 'h',
    jsxFragment: 'Fragment',
    
    // 自动为每一个被 esbuild 转换的文件注入 JSX helper
    jsxInject: `import React from 'react'`,
    
    // 最小化选项
    minifyIdentifiers: true,
    minifySyntax: true,
    minifyWhitespace: true,
  },
  
  // ==================== 静态资源处理 ====================
  // 指定额外的文件类型作为静态资源处理
  assetsInclude: ['**/*.gltf'],
  
  // ==================== 日志相关 ====================
  // 调整控制台输出的级别
  logLevel: 'info', // 'info' | 'warn' | 'error' | 'silent'
  
  // 自定义 logger
  customLogger: undefined,
  
  // 设为 false 可以避免 Vite 清屏
  clearScreen: true,
  
  // ==================== 环境变量 ====================
  // 用于加载 .env 文件的目录
  envDir: 'root',
  
  // 以此开头的环境变量会通过 import.meta.env 暴露在客户端源码中
  envPrefix: 'VITE_',
  
  // ==================== 应用类型 ====================
  // 应用类型：'spa' | 'mpa' | 'custom'
  appType: 'spa',
  
  // ==================== 未来特性 ====================
  // 启用未来的重大变更
  future: {
    // 根据破坏性变更页面配置
  },
})
